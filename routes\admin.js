var express = require('express');
var router = express.Router();
const commonService = require("../services/commonService");
const foodService = require("../services/foodService");
var adminCtr          = require('../controllers/adminController');
var dishCtr           = require('../controllers/dishController');

/* GET admin home page. */
router.get("/", commonService.isAuthenticated, adminCtr.index);
// User
router.get('/user', commonService.isAuthenticated, commonService.isAdmin, adminCtr.user);
router.get('/user', commonService.isAuthenticated, commonService.isAdmin, adminCtr.user);

router.post('/user/list', commonService.isAuthenticatedPostList, commonService.isAdminPostList, adminCtr.userList);
router.post('/user/upsert/', commonService.isAdminPost, commonService.isAdmin, adminCtr.userUpsert);
router.post('/user/delete/:id', commonService.isAdminPost, commonService.isAdmin, adminCtr.userDelete);

// Thực đơn mẫu
router.get('/thuc-don-mau', commonService.isAuthenticated, commonService.isAdmin, adminCtr.menuExampleList);
router.get('/thuc-don-mau/:id', commonService.isAuthenticated, commonService.isAdmin, adminCtr.menuExampleDetail);
router.post('/thuc-don-mau/list', commonService.isAuthenticatedPostList, commonService.isAdminPostList, adminCtr.menuExampleListData);
router.post('/thuc-don-mau/upsert/', commonService.isAdminPost, commonService.isAdmin, adminCtr.menuExampleUpsert);
router.post('/thuc-don-mau/delete/:id', commonService.isAdminPost, commonService.isAdmin, adminCtr.menuExampleDelete);

// Thực phẩm
router.get('/thuc-pham', commonService.isAuthenticated, commonService.isAdmin, adminCtr.foodList);
router.get('/thuc-pham/:id', commonService.isAuthenticated, commonService.isAdmin, adminCtr.foodDetail);
router.post('/thuc-pham/list', commonService.isAuthenticatedPostList, commonService.isAdminPostList, adminCtr.foodListData);
router.post('/thuc-pham/upsert/', commonService.isAdminPost, commonService.isAdmin, adminCtr.foodUpsert);
router.post('/thuc-pham/delete/:id', commonService.isAdminPost, commonService.isAdmin, adminCtr.foodDelete);

// Món ăn
router.get('/mon-an', commonService.isAuthenticated, commonService.isAdmin, dishCtr.list);
router.get('/mon-an/:id', commonService.isAuthenticated, commonService.isAdmin, dishCtr.detail);
router.post('/mon-an/list', commonService.isAuthenticatedPostList, commonService.isAdminPostList, dishCtr.listData);
router.post('/mon-an/upsert/', commonService.isAdminPost, commonService.isAdmin, dishCtr.upsert);
router.post('/mon-an/delete/:id', commonService.isAdminPost, commonService.isAdmin, dishCtr.delete);
router.get('/api/dishes-for-select', commonService.isAuthenticated, dishCtr.getDishesForSelect);
router.get('/api/dish-foods/:id', commonService.isAuthenticated, dishCtr.getDishFoods);

// API để lấy thông tin thực phẩm cho admin (dùng chung với khau-phan-an)
const foodRationCtr = require('../controllers/foodRationController');
router.get('/food-name', commonService.isAuthenticated, commonService.isAdmin, foodRationCtr.foodName);

router.post('/data-table', commonService.isAuthenticatedPost, commonService.isAdminPost, adminCtr.getDataEditTable);

module.exports = router;
