# Test Phân Quyền Danh Sách Dữ Liệu

## Tóm tắt thay đổi

Đã thực hiện phân quyền cho các controller để:
- **User thường**: Chỉ xem được dữ liệu do họ tạo (theo trường `created_by`)
- **Admin**: Xem được tất cả dữ liệu

## Các controller đã được cập nhật

### 1. hepatitisController
- **Bảng**: `viem_gan_td_not`, `viem_gan_td_ngt`
- **Method**: `getListTable`
- **Thay đổi**: Thêm `user: req.user` vào parameter

### 2. tetanusController  
- **Bảng**: `uon_van_kpa`
- **Method**: `getListTable`
- **Thay đổi**: Thêm `user: req.user` vào parameter

### 3. liverSurgeryController
- **<PERSON><PERSON>ng**: `cat_gan_nho_kpa`
- **Method**: `getListTable`
- **Thay đổi**: Thêm `user: req.user` vào parameter

### 4. researchController
- **Bảng**: `research`, `patients_research`
- **Method**: `getListTable`, `patientList`
- **Thay đổi**: Thêm `user: req.user` vào parameter

## Các service đã được cập nhật

### 1. commonService.getAllBoarding()
- Thêm điều kiện: `AND created_by = ?` khi user không phải admin

### 2. commonService.countAllBoarding()
- Thêm điều kiện: `AND created_by = ?` khi user không phải admin

### 3. commonService.getDataTableData()
- Thêm điều kiện: `AND created_by = ?` khi user không phải admin

## Kiểm tra cần thực hiện

### Test Case 1: Admin User
1. Đăng nhập với tài khoản admin
2. Truy cập các danh sách:
   - Viêm gan: `/viem-gan/{patient_id}/che-do-an-noi-tru`
   - Uốn ván: `/uon-van-list/{patient_id}/kpa`
   - Cắt gan nhỏ: `/hoi-chan-list/{patient_id}/kpa`
   - Nghiên cứu: `/research-list`
3. **Kết quả mong đợi**: Xem được tất cả dữ liệu

### Test Case 2: User thường
1. Đăng nhập với tài khoản user thường
2. Truy cập các danh sách tương tự
3. **Kết quả mong đợi**: Chỉ xem được dữ liệu do user đó tạo

### Test Case 3: Dữ liệu trống
1. Đăng nhập với user mới (chưa tạo dữ liệu nào)
2. Truy cập các danh sách
3. **Kết quả mong đợi**: Danh sách trống

## Cấu trúc bảng đã kiểm tra

Tất cả các bảng đều đã có trường `created_by`:
- ✅ `research` 
- ✅ `patients_research`
- ✅ `uon_van_kpa`
- ✅ `viem_gan_td_ngt`
- ✅ `viem_gan_td_not`
- ✅ `cat_gan_nho_kpa`

## Lưu ý quan trọng

1. **Dữ liệu cũ**: Các bản ghi có `created_by = NULL` sẽ không hiển thị cho user thường
2. **Tạo dữ liệu mới**: Cần đảm bảo khi tạo mới, trường `created_by` được set đúng
3. **Backup**: Nên backup database trước khi test
4. **Rollback**: Có thể rollback bằng cách xóa điều kiện `created_by` trong các method service

## Các file đã thay đổi

1. `services/commonService.js`
2. `controllers/hepatitisController.js`
3. `controllers/tetanusController.js`
4. `controllers/liverSurgeryController.js`
5. `controllers/researchController.js`
