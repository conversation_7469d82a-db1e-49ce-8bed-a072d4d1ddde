<%- include('../layout/head', {title: '<PERSON><PERSON>ản lý thực phẩm'}) %>

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <%- include('../layout/sidebar', {active: 'thuc-pham'}) %>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <%- include('../layout/header') %>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Quản lý thực phẩm</h1>
                        <a href="/admin/thuc-pham/new" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                            <i class="fas fa-plus fa-sm text-white-50"></i> Thêm thực phẩm mới
                        </a>
                    </div>

                    <!-- DataTales Example -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Danh sách thực phẩm</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Mã</th>
                                            <th>Tên thực phẩm</th>
                                            <th>Loại</th>
                                            <th>Năm</th>
                                            <th>Tên tiếng Việt</th>
                                            <th>Khối lượng (g)</th>
                                            <th>Protein (g)</th>
                                            <th>Năng lượng (kcal)</th>
                                            <th>Ngày tạo</th>
                                            <th>Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <%- include('../layout/footer') %>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript-->
    <script src="/vendor/jquery/jquery.min.js"></script>
    <script src="/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="/js/sb-admin-2.min.js"></script>

    <!-- Page level plugins -->
    <script src="/vendor/datatables/dataTables.min.js"></script>
    <script src="/vendor/datatables/dataTables.bootstrap5.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="/js/<EMAIL>"></script>

    <script>
        $(document).ready(function() {
            // Initialize DataTable
            $('#dataTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '/admin/thuc-pham/list',
                    type: 'POST',
                    data: function(d) {
                        return d;
                    }
                },
                columns: [
                    { data: 'id', width: '5%' },
                    { data: 'code', width: '8%' },
                    { data: 'name', width: '20%' },
                    { 
                        data: 'type', 
                        width: '8%',
                        className: 'text-center',
                        render: function(data, type, row) {
                            return data === 'raw' ? '<span class="badge badge-info">Sống</span>' : '<span class="badge badge-success">Chín</span>';
                        }
                    },
                    { 
                        data: 'type_year', 
                        width: '6%',
                        className: 'text-center',
                        render: function(data, type, row) {
                            return '<span class="badge badge-secondary">' + data + '</span>';
                        }
                    },
                    { data: 'ten', width: '20%' },
                    { data: 'weight', width: '8%', className: 'text-center' },
                    { data: 'protein', width: '8%', className: 'text-center' },
                    { data: 'energy', width: '8%', className: 'text-center' },
                    { 
                        data: 'created_at', 
                        width: '15%',
                        render: function(data, type, row) {
                            if (data) {
                                return moment(data).format('DD/MM/YYYY HH:mm');
                            }
                            return '';
                        }
                    },
                    {
                        data: null,
                        width: '10%',
                        orderable: false,
                        render: function(data, type, row) {
                            return `
                                <div class="btn-group" role="group">
                                    <a href="/admin/thuc-pham/${row.id}" class="btn btn-info btn-sm" title="Sửa">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="deleteFood(${row.id})" title="Xóa">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            `;
                        }
                    }
                ],
                order: [[0, 'desc']],
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/vi.json'
                }
            });
        });

        // Delete function
        function deleteFood(id) {
            Swal.fire({
                title: 'Bạn có chắc chắn?',
                text: "Dữ liệu sẽ không thể khôi phục sau khi xóa!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: `/admin/thuc-pham/delete/${id}`,
                        type: 'POST',
                        success: function(response) {
                            if (response.success) {
                                Swal.fire(
                                    'Đã xóa!',
                                    response.message,
                                    'success'
                                );
                                $('#dataTable').DataTable().ajax.reload();
                            } else {
                                Swal.fire(
                                    'Lỗi!',
                                    response.message,
                                    'error'
                                );
                            }
                        },
                        error: function() {
                            Swal.fire(
                                'Lỗi!',
                                'Có lỗi xảy ra khi xóa dữ liệu.',
                                'error'
                            );
                        }
                    });
                }
            });
        }
    </script>
    
    <!-- Moment.js for date formatting -->
    <script src="/js/moment.min.js"></script>

</body>
</html> 